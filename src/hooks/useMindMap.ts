import { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { MindMapData, NodeData, ChatMessage, ViewportState } from '../types';
import { DEFAULT_MODEL } from '../config/models';
import { useCanvasManager } from './useCanvasManager';

const API_KEY_STORAGE_KEY = 'gemini-api-key';
const SELECTED_MODEL_STORAGE_KEY = 'gemini-selected-model';

const createInitialNode = (x: number = 400, y: number = 300, searchGrounding: boolean = false): NodeData => ({
  id: uuidv4(),
  x,
  y,
  title: 'Main Topic',
  query: undefined,
  response: undefined,
  sources: undefined,
  messages: [],
  isExpanded: true,
  childIds: [],
  isSelected: false,
  width: 450,
  height: 200,
  searchGrounding,
  hasQueried: false,
});

const createInitialData = (): MindMapData => {
  const rootNode = createInitialNode();
  return {
    nodes: { [rootNode.id]: rootNode },
    rootNodeId: rootNode.id,
    searchGrounding: false,
    selectedModel: DEFAULT_MODEL.id,
    version: 1,
    lastModified: Date.now(),
  };
};

export const useMindMap = () => {
  const canvasManager = useCanvasManager();

  // Use active canvas data and viewport, with fallback to ensure data is always available
  const data = canvasManager.activeCanvas?.data;
  const viewport = canvasManager.activeCanvas?.viewport || { x: 0, y: 0, zoom: 1 };

  // If no data is available, this means the canvas manager is still initializing
  // We'll return a fallback but this should be temporary
  const fallbackData = data || createInitialData();

  const [apiKey, setApiKey] = useState<string>(() => {
    return localStorage.getItem(API_KEY_STORAGE_KEY) || '';
  });

  const [selectedModel, setSelectedModel] = useState<string>(() => {
    return localStorage.getItem(SELECTED_MODEL_STORAGE_KEY) || DEFAULT_MODEL.id;
  });

  // Update canvas data when it changes
  const setData = useCallback((newData: MindMapData | ((prev: MindMapData) => MindMapData)) => {
    if (!canvasManager.activeCanvas) return;

    const updatedData = typeof newData === 'function' ? newData(canvasManager.activeCanvas.data) : newData;
    canvasManager.updateCanvas(canvasManager.activeCanvasId, {
      data: { ...updatedData, lastModified: Date.now() }
    });
  }, [canvasManager]);

  // Update viewport
  const setViewport = useCallback((newViewport: ViewportState | ((prev: ViewportState) => ViewportState)) => {
    if (!canvasManager.activeCanvas) return;

    const updatedViewport = typeof newViewport === 'function' ? newViewport(canvasManager.activeCanvas.viewport) : newViewport;
    canvasManager.updateCanvas(canvasManager.activeCanvasId, { viewport: updatedViewport });
  }, [canvasManager]);

  useEffect(() => {
    if (apiKey) {
      localStorage.setItem(API_KEY_STORAGE_KEY, apiKey);
    } else {
      localStorage.removeItem(API_KEY_STORAGE_KEY);
    }
  }, [apiKey]);

  useEffect(() => {
    localStorage.setItem(SELECTED_MODEL_STORAGE_KEY, selectedModel);
  }, [selectedModel]);

  const updateNode = useCallback((nodeId: string, updates: Partial<NodeData>) => {
    if (!data) return;
    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: { ...prev.nodes[nodeId], ...updates },
      },
    }));
  }, [data, setData]);

  const addMessage = useCallback((nodeId: string, message: ChatMessage) => {
    if (!data) return;
    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: {
          ...prev.nodes[nodeId],
          messages: [...prev.nodes[nodeId].messages, message],
        },
      },
    }));
  }, [data, setData]);

  const setNodeQuery = useCallback((nodeId: string, query: string, response: string, sources?: Array<{title: string; uri: string}>) => {
    if (!data) return;
    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: {
          ...prev.nodes[nodeId],
          query,
          response,
          sources,
          hasQueried: true,
        },
      },
    }));
  }, [data, setData]);

  const clearNodeQuery = useCallback((nodeId: string) => {
    if (!data) return;
    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: {
          ...prev.nodes[nodeId],
          query: undefined,
          response: undefined,
          sources: undefined,
          hasQueried: false,
        },
      },
    }));
  }, [data, setData]);

  const createChildNode = useCallback((parentId: string) => {
    if (!data) return;
    const parent = data.nodes[parentId];
    if (!parent) return;

    const newNode: NodeData = {
      id: uuidv4(),
      x: parent.x + parent.width + 50, // Position to the right of the parent with a margin
      y: parent.y + parent.childIds.length * 250,
      title: 'New Topic',
      query: undefined,
      response: undefined,
      sources: undefined,
      messages: [],
      isExpanded: true,
      parentId,
      childIds: [],
      isSelected: false,
      width: 450,
      height: 200,
      searchGrounding: data.searchGrounding, // Use global default for new nodes
      hasQueried: false,
    };

    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [newNode.id]: newNode,
        [parentId]: {
          ...prev.nodes[parentId],
          childIds: [...prev.nodes[parentId].childIds, newNode.id],
        },
      },
    }));

    return newNode.id;
  }, [data, setData]);

  const deleteNode = useCallback((nodeId: string) => {
    if (!data) return;
    const node = data.nodes[nodeId];
    if (!node || nodeId === data.rootNodeId) return;

    setData(prev => {
      const newNodes = { ...prev.nodes };
      
      // Remove from parent's children
      if (node.parentId) {
        const parent = newNodes[node.parentId];
        if (parent) {
          newNodes[node.parentId] = {
            ...parent,
            childIds: parent.childIds.filter(id => id !== nodeId),
          };
        }
      }

      // Delete node and all its children recursively
      const deleteRecursively = (id: string) => {
        const nodeToDelete = newNodes[id];
        if (nodeToDelete) {
          nodeToDelete.childIds.forEach(deleteRecursively);
          delete newNodes[id];
        }
      };

      deleteRecursively(nodeId);

      return { ...prev, nodes: newNodes };
    });
  }, [data, setData]);

  const toggleSearchGrounding = useCallback(() => {
    if (!data) return;
    setData(prev => ({ ...prev, searchGrounding: !prev.searchGrounding }));
  }, [data, setData]);

  const toggleNodeSearchGrounding = useCallback((nodeId: string) => {
    if (!data) return;
    setData(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: {
          ...prev.nodes[nodeId],
          searchGrounding: !prev.nodes[nodeId].searchGrounding,
        },
      },
    }));
  }, [data, setData]);

  const clearNodeChat = useCallback((nodeId: string) => {
    updateNode(nodeId, { messages: [] });
  }, [updateNode]);

  const selectNode = useCallback((nodeId: string) => {
    if (!data) return;
    setData(prev => ({
      ...prev,
      nodes: Object.fromEntries(
        Object.entries(prev.nodes).map(([id, node]) => [
          id,
          { ...node, isSelected: id === nodeId },
        ])
      ),
    }));
  }, [data, setData]);

  const newCanvas = useCallback(() => {
    canvasManager.createCanvas();
  }, [canvasManager]);

  const exportData = useCallback(() => {
    if (!data) return;
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `mindmap-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [data]);

  const importData = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string);
        // Validate the imported data structure
        if (imported.nodes && imported.rootNodeId && imported.nodes[imported.rootNodeId]) {
          setData(imported);
          setViewport({ x: 0, y: 0, zoom: 1 }); // Reset viewport when importing
        } else {
          console.error('Invalid mind map file format');
          alert('Invalid mind map file format. Please select a valid mind map export file.');
        }
      } catch (error) {
        console.error('Failed to import data:', error);
        alert('Failed to import mind map. Please check the file format.');
      }
    };
    reader.readAsText(file);
  }, [setData, setViewport]);

  return {
    data: fallbackData,
    viewport,
    setViewport,
    updateNode,
    addMessage,
    setNodeQuery,
    clearNodeQuery,
    createChildNode,
    deleteNode,
    toggleSearchGrounding,
    toggleNodeSearchGrounding,
    clearNodeChat,
    selectNode,
    newCanvas,
    exportData,
    importData,
    apiKey,
    setApiKey,
    selectedModel,
    setSelectedModel,
    // Canvas management
    canvasManager,
  };
};